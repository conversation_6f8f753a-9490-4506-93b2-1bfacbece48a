// ======================== config keys ========================
// Analytics
export const MIXPANEL_TOKEN = "ada518bf4ef94f115a2509b8c6aeef1c";
export const CLARITY_TOKEN = "t2yv63ocw8";
export const GTM_TOKEN = "TJDZ88L9"; // Google Tag Manager

// ======================== utils keys ========================
export const WEBDOMAIN = "editpal.im";
export const WEBHOST = process.env.NODE_ENV === "production" ? `https://${WEBDOMAIN}` : "http://localhost:3000";
export const WEBNAME = "Editpal";
export const EMAIL_CONTACT = `hello@${WEBDOMAIN}`;
export const CALLBACK_URL_FAL = process.env.NODE_ENV === "production" ? `${WEBHOST}/api/webhook/fal` : "https://dev-next.xav.im/api/webhook/fal";
export const CALLBACK_URL_WAVESPEED =
	process.env.NODE_ENV === "production" ? `${WEBHOST}/api/webhook/wavespeed/` : "https://dev-next.xav.im/api/webhook/wavespeed/";

// Auth
export const ROUTE_PATH_SIGN_IN = "/";
export const ROUTE_PATH_SIGN_IN_AFTER = "/";

// OSS
export const CLOUDFLARE_R2_BUCKET_NAME = "editpal";
export const OSS_URL_HOST = `https://static.${WEBDOMAIN}`;

// Cookie
export const COOKIE_BROWSER_USER_ID = `${WEBNAME.toLowerCase()}-unia`; // 登录后把userId存入cookie，用于多次登录创建新账户，如果多次创建新账号则没有免费额度

// Duration
export const DURATION_1_HOUR = 60 * 60;
export const DURATION_2_HOUR = 2 * DURATION_1_HOUR;
export const DURATION_1_DAY = 24 * DURATION_1_HOUR;
export const DURATION_1_WEEK = 7 * DURATION_1_DAY;
export const DURATION_1_MONTH = 30 * DURATION_1_DAY;
export const DURATION_6_MONTH = 180 * DURATION_1_DAY;

// File Upload limit
export const IMAGE_SIZE_LIMIT = 1024 * 1024 * 10; // 10MB
