"use client";

import { useState, useCallback } from "react";
import { useSession } from "@/lib/auth-client";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { useUserStore } from "@/store/useUserStore";
import { useDropzone } from "react-dropzone";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, imageUrlToBase64 } from "@/lib/file/utils-file";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Loader2, Upload, X, Download, ArrowUpIcon, Trash2 } from "lucide-react";
import { toast } from "sonner";
import { AuthError } from "@/@types/error";
import { IMAGE_SIZE_LIMIT } from "@/lib/constants";
import { uploadFile } from "@/lib/file/upload-file";
import { cn } from "@/lib/utils";

interface HistoryImage {
	id: string;
	url: string;
	type: "uploaded" | "generated";
	timestamp: number;
}

export default function RemovePeopleClient() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	// 状态管理
	const [uploadedImage, setUploadedImage] = useState<string | null>(null);
	const [generatedImage, setGeneratedImage] = useState<string | null>(null);
	const [prompt, setPrompt] = useState("");
	const [isGenerating, setIsGenerating] = useState(false);
	const [uploadingImage, setUploadingImage] = useState(false);
	const [historyImages, setHistoryImages] = useState<HistoryImage[]>([]);

	// 示例图片 - 使用占位符图片
	const exampleImages = [
		"https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400&h=400&fit=crop",
		"https://images.unsplash.com/photo-1511632765486-a01980e01a18?w=400&h=400&fit=crop",
		"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop",
	];

	// 示例提示词
	const examplePrompts = ["Remove all people from the image", "Remove the person in the background", "Delete all human figures from this photo"];

	// 添加图片到历史记录
	const addToHistory = useCallback((url: string, type: "uploaded" | "generated") => {
		const newImage: HistoryImage = {
			id: Date.now().toString(),
			url,
			type,
			timestamp: Date.now(),
		};
		setHistoryImages((prev) => [newImage, ...prev]);
	}, []);

	// 处理文件上传
	const handleImageUpload = async (file: File) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}

		if (file.size > IMAGE_SIZE_LIMIT) {
			toast.warning("Image exceeds 10MB. Please upload a smaller one.");
			return;
		}

		try {
			setUploadingImage(true);
			const { file_url } = await uploadFile(file);
			setUploadedImage(file_url);
			addToHistory(file_url, "uploaded");
			toast.success("Image uploaded successfully!");
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const {
		getRootProps,
		getInputProps,
		open: openDropzone,
	} = useDropzone({
		noClick: true,
		multiple: false,
		accept: {
			"image/jpeg": [],
			"image/png": [],
			"image/webp": [],
		},
		maxFiles: 1,
		onError: console.error,
		onDrop: async (acceptedFiles, fileRejections) => {
			if (fileRejections.length > 0) {
				const message = fileRejections.at(0)?.errors.at(0)?.message;
				toast.error(message || "File rejected");
				return;
			}
			const file = acceptedFiles[0];
			if (file) {
				await handleImageUpload(file);
			}
		},
	});

	// 处理示例图片选择
	const handleExampleImageSelect = async (imageUrl: string) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		setUploadedImage(imageUrl);
		addToHistory(imageUrl, "uploaded");
		toast.success("Example image selected!");
	};

	// 处理历史图片选择
	const handleHistoryImageSelect = (imageUrl: string) => {
		setUploadedImage(imageUrl);
	};

	// 生成图片
	const handleGenerate = async () => {
		if (!uploadedImage || !prompt.trim()) {
			toast.error("Please upload an image and enter a prompt");
			return;
		}

		if (!session) {
			setSignInBoxOpen(true);
			return;
		}

		try {
			setIsGenerating(true);
			const response = await fetch("/api/v1/image/remove-object", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					prompt: prompt.trim(),
					image: uploadedImage,
				}),
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.message || "Generation failed");
			}

			if (data.resultUrls && data.resultUrls.length > 0) {
				const resultUrl = data.resultUrls[0];
				setGeneratedImage(resultUrl);
				addToHistory(resultUrl, "generated");
				toast.success("Image generated successfully!");
			} else {
				throw new Error("No result image received");
			}
		} catch (error: any) {
			console.error("Generation failed:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Generation failed: ${error.message}`);
		} finally {
			setIsGenerating(false);
		}
	};

	// 下载功能
	const handleDownload = async (imageUrl: string) => {
		try {
			const base64 = await imageUrlToBase64(imageUrl);
			if (userHasPaid) {
				await downloadImageFromBase64(base64, "removed-people");
			} else {
				await downloadImageFromBase64WithWatermark(base64, "removed-people");
			}
		} catch (error) {
			console.error("Download failed:", error);
			toast.error("Download failed");
		}
	};

	const handleDownloadWithoutWatermark = () => {
		setPlanBoxOpen(true);
	};

	// 删除上传的图片
	const handleRemoveImage = () => {
		setUploadedImage(null);
		setGeneratedImage(null);
	};

	// 将生成的图片作为输入
	const handleUseAsInput = (imageUrl: string) => {
		setUploadedImage(imageUrl);
		setGeneratedImage(null);
		addToHistory(imageUrl, "uploaded");
	};

	return (
		<div className="mx-auto w-full max-w-6xl px-4">
			<div className="grid grid-cols-1 gap-6 lg:grid-cols-2 lg:gap-8">
				{/* 左侧 */}
				<div className="space-y-6">
					{!uploadedImage ? (
						// 初始状态 - 展示效果图片
						<div className="flex aspect-square items-center justify-center rounded-lg bg-gray-100">
							<div className="space-y-4 text-center">
								<div className="mx-auto flex h-24 w-24 items-center justify-center rounded-lg bg-gray-200">
									<Upload className="h-8 w-8 text-gray-400" />
								</div>
								<div>
									<h3 className="text-lg font-medium text-gray-900">Remove People from Photos</h3>
									<p className="mt-1 text-sm text-gray-500">Upload an image to get started</p>
								</div>
							</div>
						</div>
					) : (
						// 上传后状态
						<div className="space-y-4">
							{/* 图片显示 */}
							<div className="relative">
								<img src={uploadedImage} alt="Uploaded" className="aspect-square w-full rounded-lg object-cover" />
								<Button variant="destructive" size="icon" className="absolute top-2 right-2 h-8 w-8" onClick={handleRemoveImage}>
									<X className="h-4 w-4" />
								</Button>
							</div>

							{/* Prompt输入框 */}
							<div className="space-y-3">
								<Textarea
									placeholder="Describe what you want to remove from the image..."
									value={prompt}
									onChange={(e) => setPrompt(e.target.value)}
									className="min-h-[100px] resize-none"
									maxLength={500}
								/>

								{/* 示例提示词 */}
								<div className="space-y-2">
									<p className="text-sm text-gray-600">Try these examples:</p>
									<div className="flex flex-wrap gap-2">
										{examplePrompts.map((example, index) => (
											<Button key={index} variant="outline" size="sm" className="text-xs" onClick={() => setPrompt(example)}>
												{example}
											</Button>
										))}
									</div>
								</div>
							</div>

							{/* 生成按钮 */}
							<Button onClick={handleGenerate} disabled={!prompt.trim() || isGenerating || !session} className="w-full" size="lg">
								{isGenerating ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										Generating...
									</>
								) : !session ? (
									"Sign in to Generate"
								) : (
									<>
										<ArrowUpIcon className="mr-2 h-4 w-4" />
										Generate
									</>
								)}
							</Button>
						</div>
					)}
				</div>

				{/* 右侧 */}
				<div className="space-y-6">
					{!uploadedImage ? (
						// 初始状态 - 上传区域
						<Card className="p-6">
							<div {...getRootProps()} className="space-y-4">
								<input {...getInputProps()} />

								{/* 上传区域 */}
								<div
									className={cn(
										"cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-8 text-center transition-colors",
										"hover:border-gray-400 hover:bg-gray-50",
									)}
									onClick={openDropzone}
								>
									{uploadingImage ? (
										<div className="space-y-2">
											<Loader2 className="mx-auto h-8 w-8 animate-spin text-gray-400" />
											<p className="text-sm text-gray-500">Uploading...</p>
										</div>
									) : (
										<div className="space-y-2">
											<Upload className="mx-auto h-8 w-8 text-gray-400" />
											<div>
												<p className="text-sm font-medium text-gray-900">Click to upload image</p>
												<p className="text-xs text-gray-500">or drag and drop</p>
											</div>
											<Button variant="outline" size="sm" type="button">
												Choose File
											</Button>
										</div>
									)}
								</div>

								{/* 示例图片 */}
								<div className="space-y-3">
									<p className="text-sm text-gray-600">No image? Try one of these:</p>
									<div className="grid grid-cols-3 gap-3">
										{exampleImages.map((image, index) => (
											<div
												key={index}
												className="aspect-square cursor-pointer rounded-lg bg-gray-200 transition-opacity hover:opacity-80"
												onClick={() => handleExampleImageSelect(image)}
											>
												<img
													src={image}
													alt={`Example ${index + 1}`}
													className="h-full w-full rounded-lg object-cover"
													onError={(e) => {
														// 如果示例图片加载失败，显示占位符
														e.currentTarget.style.display = "none";
													}}
												/>
											</div>
										))}
									</div>
								</div>
							</div>
						</Card>
					) : (
						// 上传后状态 - 生成结果区域
						<Card className="p-6">
							{generatedImage ? (
								<div className="space-y-4">
									<div className="relative">
										<img src={generatedImage} alt="Generated result" className="aspect-square w-full rounded-lg object-cover" />
									</div>

									{/* 操作按钮 */}
									<div className="flex gap-2">
										<Button variant="outline" size="sm" onClick={() => handleUseAsInput(generatedImage)} className="flex-1">
											Use as Input
										</Button>

										{userHasPaid ? (
											<Button variant="outline" size="sm" onClick={() => handleDownload(generatedImage)} className="flex-1">
												<Download className="mr-2 h-4 w-4" />
												Download
											</Button>
										) : (
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button variant="outline" size="sm" className="flex-1">
														<Download className="mr-2 h-4 w-4" />
														Download
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent>
													<DropdownMenuItem onClick={handleDownloadWithoutWatermark}>Download without watermark</DropdownMenuItem>
													<DropdownMenuItem onClick={() => handleDownload(generatedImage)}>Download with watermark</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										)}
									</div>
								</div>
							) : (
								<div className="flex aspect-square items-center justify-center rounded-lg bg-gray-100">
									<div className="space-y-2 text-center">
										<div className="mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-gray-200">
											<ArrowUpIcon className="h-6 w-6 text-gray-400" />
										</div>
										<p className="text-sm text-gray-500">Generated image will appear here</p>
									</div>
								</div>
							)}
						</Card>
					)}

					{/* 历史记录 */}
					{historyImages.length > 0 && (
						<Card className="p-4">
							<h3 className="mb-3 text-sm font-medium text-gray-900">History</h3>
							<div className="grid grid-cols-4 gap-2">
								{historyImages.slice(0, 8).map((historyImage) => (
									<div
										key={historyImage.id}
										className="group relative aspect-square cursor-pointer rounded bg-gray-200 transition-opacity hover:opacity-80"
										onClick={() => handleHistoryImageSelect(historyImage.url)}
									>
										<img src={historyImage.url} alt="History" className="h-full w-full rounded object-cover" />
										<div className="bg-opacity-0 group-hover:bg-opacity-20 absolute inset-0 flex items-center justify-center rounded bg-black transition-all">
											<div className={cn("h-2 w-2 rounded-full", historyImage.type === "uploaded" ? "bg-blue-500" : "bg-green-500")} />
										</div>
									</div>
								))}
							</div>
						</Card>
					)}
				</div>
			</div>
		</div>
	);
}
