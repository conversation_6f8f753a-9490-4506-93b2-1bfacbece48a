"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, Check, X, Info, RocketIcon } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { pricingPlans, PricingPlan } from "@/config/pricing";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Subscription } from "@/@types/subscription";
import { AuthError, handleError } from "@/@types/error";
import { useRouter } from "nextjs-toploader/app";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ofetch } from "ofetch";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { canChangePlan } from "@/lib/utils-membership";
import { EVENT_CHECKOUT } from "@/lib/track-events";
import { sendGTMEvent } from "@next/third-parties/google";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Hint } from "../ui/custom/hint";
import { useSession } from "@/lib/auth-client";

export default function Plans({ hasFree = false, userSubscription }: { hasFree?: boolean; userSubscription?: Subscription | null }) {
	const { data: session } = useSession();
	const router = useRouter();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();

	const [currentTab, setCurrentTab] = useState("yearly");

	// const [isCancelling, setIsCancelling] = useState(false);
	const [isPurchasing, setIsPurchasing] = useState(false);
	const [purchaseProductId, setPurchaseProductId] = useState<string | null>(null);
	const [subscriptionChanged, setSubscriptionChanged] = useState(false);

	const purchase = async (productId: string, planName?: string) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		if (userSubscription) return;
		console.log("New subscription checkout");
		sendGTMEvent({ event: EVENT_CHECKOUT, checkout_plan: `${planName}(${currentTab === "monthly" ? "Monthly" : "Yearly"})` });

		try {
			setIsPurchasing(true);
			setPurchaseProductId(productId);

			const { status, message, url } = await ofetch("/api/payment/checkout", {
				method: "POST",
				body: { productId, type: "subscription" },
			});
			if (status === 1001) {
				toast.error("You are already subscribed.");
				setIsPurchasing(false);
				return;
			}
			handleError(status, message);
			if (url) {
				router.push(url);
				// window.open(url, "_self");
			}
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
			} else {
				toast.error("Error creating a checkout.");
			}
			setIsPurchasing(false);
			setPurchaseProductId(null);
		}
	};
	const changePlan = async (productId: string) => {
		if (!userSubscription) return;
		if (userSubscription.productId === productId) return;
		if (isPurchasing) return;
		console.log("Change subscription");

		try {
			setPurchaseProductId(productId);
			setIsPurchasing(true);
			const { status, message, checkoutId } = await ofetch("/api/payment/subscription", {
				method: "POST",
				body: {
					type: "change",
					subscriptionId: userSubscription.subscriptionId,
					productId,
				},
			});
			handleError(status, message);
			if (checkoutId) {
				toast.success("Your subscription change has been initiated.");
				router.push(`/user/confirmation?checkout_id=${checkoutId}`);
			} else {
				setSubscriptionChanged(true);
			}
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error("Subscription change failed.");
		} finally {
			setIsPurchasing(false);
			setPurchaseProductId(null);
		}
	};
	// const cancelSubscription = async () => {
	// 	if (!subscription) return;
	// 	if (isCancelling) return;

	// 	try {
	// 		setIsCancelling(true);

	// 		const { status, message } = await ofetch("/api/v1/user/subscription/cancel", {
	// 			method: "POST",
	// 			body: { subscriptionId: subscription.subscriptionId },
	// 		});
	// 		handleError(status, message);
	// 		// const { status, message } = await cancelSubAction(subscription.subscriptionId);
	// 		// handleActionError(status, message);
	// 		router.refresh();
	// 		toast.success("Subscription cancelled.");
	// 		setUserPlanBoxOpen(false);
	// 		setUserSubscription(null);
	// 	} catch (error) {
	// 		console.error(error);
	// 		if (error instanceof AuthError) {
	// 			setSignInBoxOpen(true);
	// 			return;
	// 		}
	// 		toast.error("Error cancelling subscription.");
	// 	} finally {
	// 		setIsCancelling(false);
	// 	}
	// };

	return (
		<div className="relative flex w-full flex-col gap-12">
			<div className="flex items-center justify-center">
				<Tabs value={currentTab} onValueChange={setCurrentTab} className="">
					<TabsList className="h-[46px] p-1">
						<TabsTrigger
							value="monthly"
							className="data-[state=active]:bg-secondary-foreground data-[state=active]:text-secondary px-4 font-normal"
						>
							Monthly
						</TabsTrigger>
						<TabsTrigger value="yearly" className="data-[state=active]:bg-secondary-foreground data-[state=active]:text-secondary px-4 font-normal">
							Yearly <span className="text-foreground rounded-full bg-yellow-300 px-2 py-1 text-[10px] font-medium">Save 30%+</span>
						</TabsTrigger>
					</TabsList>
				</Tabs>
			</div>

			{subscriptionChanged && (
				<div className="absolute z-20 w-full">
					<Alert className="mx-auto max-w-xl items-center shadow-xl [&>svg]:text-blue-500">
						<RocketIcon className="0 h-4 w-4" />
						<AlertTitle className="">Your subscription change has been initiated.</AlertTitle>
						<AlertDescription className="text-muted-foreground inline text-sm">
							Your order is being processed. It should be completed in 20 seconds to 2 minutes. You can{" "}
							<span className="inline text-blue-500">refresh the page to check the status</span>.
						</AlertDescription>
					</Alert>
				</div>
			)}

			<div
				className={cn(
					"mx-auto inline-grid w-full grid-cols-1 justify-center gap-4 text-start sm:grid-cols-2 md:gap-6",
					hasFree ? "lg:grid-cols-4" : "lg:grid-cols-3",
				)}
			>
				{pricingPlans.map((pricing: PricingPlan, index: number) => {
					if (!hasFree && pricing.free) return null;
					return (
						<Card
							key={index}
							className={cn(
								"bg-muted relative mx-auto w-full max-w-full gap-1 border-none shadow-none",
								// pricing.badge && "border-brand-success border",
							)}
						>
							<CardHeader className="h-20 gap-0">
								<CardTitle className="flex flex-col gap-1">
									<div className="flex flex-row items-center justify-between gap-1 text-xl font-medium">
										<p className={pricing.titleClassName}>{pricing.title}</p>
										{pricing.badge && <Badge className="bg-blue-500 bg-linear-to-r font-normal hover:bg-blue-500">{pricing.badge}</Badge>}
									</div>
									{pricing.description && <p className="text-muted-foreground text-sm font-normal">{pricing.description}</p>}
								</CardTitle>
							</CardHeader>

							<CardContent className="flex flex-col gap-0">
								<div className="mt-4 mb-10">
									<div className="flex flex-wrap items-end">
										{!pricing.free && currentTab === "yearly" && (
											<span className="text-muted-foreground mr-2 text-xl line-through">
												{pricing.currency.symbol}
												{pricing.price.monthly}
											</span>
										)}
										<span className="text-4xl">
											{pricing.currency.symbol}
											{pricing.free || currentTab === "monthly" ? pricing.price.monthly : pricing.price.monthForYearly}
										</span>
										<div className="mb-1 ml-1 leading-none">
											<p className="text-muted-foreground font- text-sm">{!pricing.free && pricing.duration}</p>
										</div>
									</div>
									<p className="text-muted-foreground text-xs font-normal">billed {currentTab}</p>
								</div>

								<div className="h-20">
									{pricing.free ? (
										<Button disabled variant="secondary" className="bg-input hover:bg-input w-full max-w-xs rounded-full">
											Get started
										</Button>
									) : !userSubscription ? (
										<Button
											{...{
												// variant: pricing.badge ? "secondary" : "default",
												disabled:
													isPurchasing &&
													purchaseProductId === (currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly),
											}}
											className={cn(
												"w-full rounded-full",
												pricing.badge ? "bg-blue-500 hover:bg-blue-500/80" : "bg-foreground hover:bg-foreground/80",
											)}
											onClick={() => {
												purchase(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly, pricing.title);
											}}
										>
											<p className="flex flex-row items-center space-x-1">
												<span>Subscribe now</span>
												{isPurchasing &&
													purchaseProductId ===
														(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) && (
														<Loader2 className="h-4 w-4 animate-spin" />
													)}
											</p>
										</Button>
									) : userSubscription.productId === (currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) ? (
										<Button
											// {...{
											// 	variant: pricing.badge ? "secondary" : "default",
											// }}
											disabled
											className={cn(
												"w-full rounded-full",
												pricing.badge ? "bg-blue-500 hover:bg-blue-500/80" : "bg-foreground hover:bg-foreground/80",
											)}
										>
											<p className="flex flex-row items-center space-x-1">Current plan</p>
										</Button>
									) : (
										<AlertDialog>
											<AlertDialogTrigger asChild>
												<Button
													{...{
														// variant: pricing.badge ? "secondary" : "default",
														disabled:
															(isPurchasing &&
																purchaseProductId ===
																	(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly)) ||
															!canChangePlan(
																userSubscription.productId,
																currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly,
															),
													}}
													className={cn(
														"w-full rounded-full",
														pricing.badge ? "bg-blue-500 hover:bg-blue-500/80" : "bg-foreground hover:bg-foreground/80",
													)}
												>
													<p className="flex flex-row items-center space-x-1">
														<span>Change</span>
														{isPurchasing &&
															purchaseProductId ===
																(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly) && (
																<Loader2 className="h-4 w-4 animate-spin" />
															)}
													</p>
												</Button>
											</AlertDialogTrigger>
											<AlertDialogContent>
												<AlertDialogHeader>
													<AlertDialogTitle>Are you sure you want to change your subscription plan?</AlertDialogTitle>
													<AlertDialogDescription>
														Your plan will change right away. We’ll adjust your bill to match your new plan.
													</AlertDialogDescription>
												</AlertDialogHeader>
												<AlertDialogFooter>
													<AlertDialogCancel>Cancel</AlertDialogCancel>
													<AlertDialogAction
														onClick={() => {
															changePlan(currentTab === "monthly" ? pricing.productId!.monthly : pricing.productId!.yearly);
														}}
													>
														Change
													</AlertDialogAction>
												</AlertDialogFooter>
											</AlertDialogContent>
										</AlertDialog>
									)}
								</div>

								<div className="flex flex-col gap-4">
									<div className="text-secondary-foreground flex flex-col gap-3 text-[14px]">
										{pricing.features?.map((feature: any, index: any) => (
											<div key={index} className="flex items-start gap-2">
												<Check className="text-secondary-foreground mt-0.5 h-4 w-4 shrink-0" />
												<p className="flex w-full items-start justify-between gap-1">
													<span>{feature.description}</span>
													{feature.tips && (
														<Hint label={feature.tips}>
															<Info className="text-muted-foreground mt-0.5 inline size-4 cursor-pointer" />
														</Hint>
													)}
												</p>
											</div>
										))}

										{pricing.unFeatures?.map((feature: any, index: any) => (
											<p key={index} className="flex items-start gap-3">
												<X className="mt-0.5 h-4 w-4 shrink-0 text-red-700" />
												<span className="">{feature}</span>
											</p>
										))}
									</div>
								</div>
							</CardContent>

							<div className={cn(pricing.badge && "h-4")}></div>
						</Card>
					);
				})}
			</div>
		</div>
	);
}
